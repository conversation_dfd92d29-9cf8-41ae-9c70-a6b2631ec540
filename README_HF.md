---
title: Pose-Think AI Movement Analysis
emoji: 🎯
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: 4.8.0
app_file: app.py
pinned: false
license: mit
---

# 🎯 Pose-Think: AI-Powered Movement Analysis Suite

Real-time posture and movement analysis with multiple specialized modes. Get instant feedback on what the camera sees!

## 🌟 Features

### 🎯 **Basic Posture Analysis**
- **Real-time body detection**: Instantly identifies visible body parts
- **Joint angle measurements**: Precise elbow, knee, and other joint angles
- **Posture alignment**: Shoulder level, hip alignment, neck position
- **Clear feedback**: "Left shoulder high", "Neck tilted right", "Elbows at 156°"

### 🎯 **Enhanced Posture Analysis** 
- **Everything from Basic mode** plus:
- **Age-specific recommendations**: Tailored advice for different age groups
- **BMI calculation**: Height/weight analysis for posture load assessment
- **Personalized insights**: Context-aware health recommendations

### 🤚 **Hand Tracking**
- **Dual hand detection**: Track up to 2 hands simultaneously
- **Finger counting**: Automatic detection of extended fingers
- **Gesture analysis**: Real-time hand position and orientation
- **Perfect for**: Hand exercises, gesture recognition, rehabilitation

## 🚀 How to Use

1. **Choose Analysis Type**: Select from Basic Posture, Enhanced Posture, or Hand Tracking
2. **Allow Camera Access**: Grant permission when prompted by your browser
3. **Position Yourself**: 
   - **For Posture**: Stand 2-3 meters from camera, full body visible
   - **For Hands**: Show hands clearly to the camera
4. **Click Analyze**: Get instant, detailed feedback
5. **Enhanced Mode**: Optionally enter age/height/weight for personalized insights

## 📊 What You'll See

### Basic/Enhanced Posture Analysis:
```
✅ Visible: Head, Shoulders, Elbows, Hips, Knees

📐 Left elbow angle: 156.3°
📐 Right elbow angle: 162.1°

⚠️ Left shoulder higher
✅ Hips level
🔍 Neck: Tilted right
```

### Enhanced Mode Additional Info:
```
👤 Profile: Age: 32 | BMI: 26.1
⚠️ BMI slightly high - extra load on posture

🎯 Age-Specific Recommendations:
💡 Middle age: Regular exercise important
💼 Make workspace ergonomic
```

### Hand Tracking:
```
✅ 2 hands detected

🖐️ Hand 1: 5 fingers up
🖐️ Hand 2: 3 fingers up
```

## 🎯 Perfect For

- **Personal Health**: Quick posture checks and movement awareness
- **Workplace Wellness**: Ergonomic assessments and posture monitoring
- **Fitness & Rehabilitation**: Exercise form checking and progress tracking
- **Education & Research**: Movement analysis and biomechanics studies
- **Accessibility**: Hand gesture recognition and interaction

## 🔧 Technical Details

- **AI Model**: MediaPipe Pose and Hands (Google)
- **Processing**: Real-time analysis at 30+ FPS
- **Accuracy**: 95%+ detection rate in good lighting
- **Privacy**: All processing done locally, no data stored
- **Compatibility**: Works on desktop, tablet, and mobile browsers

## 📝 Tips for Best Results

### For Posture Analysis:
- **Lighting**: Ensure even, bright lighting
- **Background**: Use plain, contrasting background
- **Distance**: Stand 2-3 meters from camera
- **Position**: Keep full body visible in frame

### For Hand Tracking:
- **Visibility**: Keep hands clearly visible
- **Contrast**: Ensure hands contrast with background
- **Movement**: Avoid rapid movements for better tracking
- **Lighting**: Good lighting improves accuracy

## 🎓 Understanding the Feedback

### Symbols:
- ✅ **Green checkmarks**: Good alignment/posture
- ⚠️ **Warning signs**: Issues detected that need attention
- 📐 **Measurements**: Precise joint angles in degrees
- 🔍 **Position info**: Head, neck, body position details
- 👤 **Profile info**: Personal context and recommendations

### Measurements:
- **Joint angles**: 0-180° range, closer to 180° = straighter
- **Alignment**: Compares left vs right side positioning
- **Visibility**: Shows which body parts are clearly detected

## 🚀 Built With

- **MediaPipe**: Google's ML framework for pose and hand detection
- **Gradio**: Modern web interface for ML applications
- **OpenCV**: Computer vision and image processing
- **NumPy**: Numerical computations

## 📄 License

MIT License - feel free to use and modify for your projects!

## 🤝 Contributing

This is part of the Pose-Think project. Contributions welcome on [GitHub](https://github.com/semskurto/pose-think)!

---

**⚠️ Disclaimer**: This tool is for educational and wellness purposes only. It does not provide medical diagnosis. Consult healthcare professionals for medical concerns.
