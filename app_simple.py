# Pose-Think: AI-Powered Movement Analysis Suite - Hugging Face Compatible
import cv2
import mediapipe as mp
import gradio as gr
import numpy as np

# MediaPipe initialization
mp_pose = mp.solutions.pose
mp_hands = mp.solutions.hands
mp_drawing = mp.solutions.drawing_utils

def analyze_posture(image, analysis_type="basic"):
    """Main analysis function"""
    if image is None:
        return None, "❌ No image / Görüntü yok"
    
    # Convert BGR to RGB
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    output_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)
    
    feedback = []
    
    if analysis_type == "hand":
        # Hand analysis
        with mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        ) as hands:
            results = hands.process(rgb_image)
            
            if results.multi_hand_landmarks:
                hand_count = len(results.multi_hand_landmarks)
                feedback.append(f"✅ {hand_count} el tespit edildi / {hand_count} hand(s) detected")
                
                for idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                    mp_drawing.draw_landmarks(output_image, hand_landmarks, mp_hands.HAND_CONNECTIONS)
                    
                    # Simple finger counting
                    landmarks = hand_landmarks.landmark
                    fingers_up = 0
                    tip_ids = [4, 8, 12, 16, 20]
                    pip_ids = [3, 6, 10, 14, 18]
                    
                    for i in range(5):
                        if landmarks[tip_ids[i]].y < landmarks[pip_ids[i]].y:
                            fingers_up += 1
                    
                    feedback.append(f"🖐️ El {idx+1}: {fingers_up} parmak / Hand {idx+1}: {fingers_up} fingers")
            else:
                feedback.append("❌ El tespit edilemedi / No hands detected")
    
    else:
        # Posture analysis
        with mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=False,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        ) as pose:
            results = pose.process(rgb_image)
            
            if results.pose_landmarks:
                mp_drawing.draw_landmarks(output_image, results.pose_landmarks, mp_pose.POSE_CONNECTIONS)
                
                landmarks = results.pose_landmarks.landmark
                visible_parts = []
                
                # Check visible parts
                if landmarks[mp_pose.PoseLandmark.NOSE.value].visibility > 0.5:
                    visible_parts.append("Baş/Head")
                
                # Shoulders
                left_shoulder = landmarks[mp_pose.PoseLandmark.LEFT_SHOULDER.value]
                right_shoulder = landmarks[mp_pose.PoseLandmark.RIGHT_SHOULDER.value]
                
                if left_shoulder.visibility > 0.5 and right_shoulder.visibility > 0.5:
                    visible_parts.append("Omuzlar/Shoulders")
                    
                    # Shoulder level check
                    shoulder_diff = abs(left_shoulder.y - right_shoulder.y)
                    if shoulder_diff > 0.05:
                        if left_shoulder.y < right_shoulder.y:
                            feedback.append("⚠️ Sol omuz yüksek / Left shoulder high")
                        else:
                            feedback.append("⚠️ Sağ omuz yüksek / Right shoulder high")
                    else:
                        feedback.append("✅ Omuzlar seviyeli / Shoulders level")
                
                # Elbows
                left_elbow = landmarks[mp_pose.PoseLandmark.LEFT_ELBOW.value]
                right_elbow = landmarks[mp_pose.PoseLandmark.RIGHT_ELBOW.value]
                
                if left_elbow.visibility > 0.5 and right_elbow.visibility > 0.5:
                    visible_parts.append("Dirsekler/Elbows")
                    
                    # Simple angle calculation
                    try:
                        def calculate_angle(a, b, c):
                            a = np.array(a)
                            b = np.array(b)
                            c = np.array(c)
                            radians = np.arctan2(c[1] - b[1], c[0] - b[0]) - np.arctan2(a[1] - b[1], a[0] - b[0])
                            angle = np.abs(radians * 180.0 / np.pi)
                            if angle > 180.0:
                                angle = 360 - angle
                            return angle
                        
                        # Left elbow angle
                        left_shoulder_pos = [left_shoulder.x, left_shoulder.y]
                        left_elbow_pos = [left_elbow.x, left_elbow.y]
                        left_wrist_pos = [landmarks[mp_pose.PoseLandmark.LEFT_WRIST.value].x,
                                         landmarks[mp_pose.PoseLandmark.LEFT_WRIST.value].y]
                        
                        left_angle = calculate_angle(left_shoulder_pos, left_elbow_pos, left_wrist_pos)
                        feedback.append(f"📐 Sol dirsek / Left elbow: {left_angle:.1f}°")
                        
                        # Right elbow angle
                        right_shoulder_pos = [right_shoulder.x, right_shoulder.y]
                        right_elbow_pos = [right_elbow.x, right_elbow.y]
                        right_wrist_pos = [landmarks[mp_pose.PoseLandmark.RIGHT_WRIST.value].x,
                                          landmarks[mp_pose.PoseLandmark.RIGHT_WRIST.value].y]
                        
                        right_angle = calculate_angle(right_shoulder_pos, right_elbow_pos, right_wrist_pos)
                        feedback.append(f"📐 Sağ dirsek / Right elbow: {right_angle:.1f}°")
                        
                    except:
                        feedback.append("⚠️ Açı hesaplanamadı / Cannot calculate angles")
                
                # Hips
                left_hip = landmarks[mp_pose.PoseLandmark.LEFT_HIP.value]
                right_hip = landmarks[mp_pose.PoseLandmark.RIGHT_HIP.value]
                
                if left_hip.visibility > 0.5 and right_hip.visibility > 0.5:
                    visible_parts.append("Kalçalar/Hips")
                    
                    hip_diff = abs(left_hip.y - right_hip.y)
                    if hip_diff > 0.03:
                        if left_hip.y < right_hip.y:
                            feedback.append("⚠️ Sol kalça yüksek / Left hip high")
                        else:
                            feedback.append("⚠️ Sağ kalça yüksek / Right hip high")
                    else:
                        feedback.append("✅ Kalçalar seviyeli / Hips level")
                
                # Neck position
                nose = landmarks[mp_pose.PoseLandmark.NOSE.value]
                if nose.visibility > 0.5:
                    shoulder_center_x = (left_shoulder.x + right_shoulder.x) / 2
                    head_offset = abs(nose.x - shoulder_center_x)
                    
                    if head_offset > 0.08:
                        if nose.x < shoulder_center_x:
                            feedback.append("🔍 Boyun sola eğik / Neck tilted left")
                        else:
                            feedback.append("🔍 Boyun sağa eğik / Neck tilted right")
                    else:
                        feedback.append("🔍 Boyun merkezi / Neck centered")
                
                # List visible parts
                if visible_parts:
                    feedback.insert(0, f"✅ Görünen / Visible: {', '.join(visible_parts)}")
                    feedback.insert(1, "")
            else:
                feedback.append("❌ Vücut tespit edilemedi / Body not detected")
                feedback.append("📍 Tam vücut görünür olsun / Keep full body visible")
    
    return output_image, "\n".join(feedback)

# Gradio Interface
demo = gr.Interface(
    fn=analyze_posture,
    inputs=[
        gr.Image(sources=["webcam"], label="📹 Camera"),
        gr.Radio(
            choices=[
                ("🎯 Basic Posture", "basic"),
                ("🤚 Hand Tracking", "hand")
            ],
            value="basic",
            label="Analysis Type"
        )
    ],
    outputs=[
        gr.Image(label="🎯 Analysis Result"),
        gr.Textbox(label="📊 Feedback", lines=10)
    ],
    title="🎯 Pose-Think: AI Movement Analysis",
    description="""
    **Real-time posture and movement analysis**
    
    - **Basic Posture**: Body parts, joint angles, alignment feedback
    - **Hand Tracking**: Hand detection and finger counting
    
    **Instructions:**
    1. Choose analysis type
    2. Allow camera access
    3. Position yourself 2-3 meters from camera
    4. Get instant feedback on what the camera sees!
    """,
    live=False
)

if __name__ == "__main__":
    demo.launch()
