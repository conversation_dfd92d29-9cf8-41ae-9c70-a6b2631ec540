# 🏥 Advanced Physiotherapy Assessment System

AI-powered comprehensive posture analysis and biomechanical assessment system. Provides professional physiotherapist-standard 33-point body analysis, risk assessment, and personalized treatment plans.

## 🌟 Features

### 🎯 Comprehensive Analysis
- **33-Point Body Analysis**: Full body landmark detection with MediaPipe Pose
- **Real-Time Biomechanical Assessment**: Instant posture and movement analysis
- **Risk Assessment**: Categorical analysis of injury risks (Low/Moderate/High/Critical)
- **Compensation Pattern Detection**: Upper/Lower Crossed Syndrome and lateral chain dysfunctions

### 🏥 Clinical Standard Assessment
- **Physiotherapist Perspective**: Professional clinical assessment criteria
- **Functional Scoring**: Neck, shoulder, spinal, pelvic, and lower extremity function scores
- **Detailed Joint Angle Analysis**: 3D space angle calculations
- **Symmetry Analysis**: Right-left comparative assessment

### 💊 Personalized Treatment Plans
- **Individual Exercise Programs**: Customized based on age and experience level
- **Clinical Treatment Protocols**: Evidence-based physiotherapy approaches
- **Progressive Exercise Planning**: Gradual difficulty progression
- **Follow-up and Assessment Program**: Regular monitoring recommendations

### 🎨 Advanced User Interface
- **Professional Design**: Modern interface suitable for clinical environments
- **Real-Time Visualization**: Live landmark drawing and analysis
- **Interactive Controls**: Sensitivity settings and patient profile
- **Responsive Design**: Compatible view across all devices

## 🔬 Technology Stack

- **AI/ML**: MediaPipe Pose v2, Advanced biomechanical algorithms
- **Web Framework**: Modern web interface with Gradio
- **Image Processing**: OpenCV, NumPy
- **Clinical Modules**: Custom physiotherapy algorithms
- **UI/UX**: CSS3 gradients, responsive design

## 📦 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/semskurto/pose-think.git
cd pose-think
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run the Application
```bash
# Main physiotherapy assessment system
python physiotherapy_assessment_app.py

# Basic hand tracking (legacy version)
python hand_tracking_app.py
```

## 🎯 User Guide

### 1️⃣ Preparation
- Stand so your full body is visible to the camera
- Use a plain, light-colored background
- Ensure adequate lighting

### 2️⃣ Position
- Feet shoulder-width apart
- Arms relaxed at your sides
- Maintain natural standing posture

### 3️⃣ Assessment
- System automatically analyzes your posture
- Receive real-time feedback
- Generate your personalized exercise plan

### 4️⃣ Treatment Plan
- Enter your patient profile (age, experience)
- Click "Clinical Treatment Plan" button
- Receive your detailed treatment protocol

## 📊 Analysis Parameters

### 🔍 Measured Angles
- **Cervical**: Forward head posture, neck inclination
- **Shoulder**: Protraction, asymmetry, blade position
- **Spinal**: Lateral tilt, kyphosis/lordosis curves
- **Pelvic**: Tilt angle, hip asymmetry
- **Lower Extremity**: Knee angles, valgus/varus deformities

### ⚖️ Symmetry Analysis
- Right-left shoulder height difference
- Hip level comparison
- Extremity length analysis
- Postural balance assessment

### 🎯 Scoring System
- **90-100**: Excellent posture
- **80-89**: Good posture
- **70-79**: Moderate issues
- **60-69**: Issues requiring attention
- **<60**: Professional assessment recommended

## 🏥 Clinical Modules

### 📋 Assessment Algorithms (`advanced_physiotherapy_algorithms.py`)
- Cervical spine analysis
- Shoulder complex assessment
- Spinal alignment control
- Pelvic stability analysis
- Lower extremity biomechanics

### 💊 Clinical Feedback System (`clinical_feedback_system.py`)
- Exercise database (150+ exercises)
- Treatment protocols
- Risk stratification
- Progression planning
- Follow-up programs

### 🎨 Main Application (`physiotherapy_assessment_app.py`)
- Real-time analysis engine
- User interface
- Data integration
- Report generation

## 🔧 System Requirements

- **Python**: 3.7+
- **Camera**: Webcam or external camera
- **Browser**: Modern web browser (Chrome, Firefox, Safari)
- **Operating System**: Windows, macOS, Linux
- **RAM**: Minimum 4GB (8GB recommended)
- **Processor**: Multi-core CPU (GPU optional)

## 📚 Dependencies

```txt
opencv-python>=4.8.0      # Image processing
mediapipe>=0.10.0         # AI pose estimation
gradio>=4.0.0             # Web interface
numpy>=1.21.0             # Numerical computations
Pillow>=9.0.0             # Image manipulation
```

## 🎓 Physiotherapy Principles

### 🔍 Assessment Criteria
- **Posture Analysis**: Sagittal and frontal plane assessment
- **Movement Quality**: Dynamic analysis and compensation detection
- **Functional Capacity**: Activities of daily living assessment

### 💊 Treatment Approaches
- **Conservative Treatment**: Exercise, education, ergonomics
- **Evidence-Based**: Evidence-based physiotherapy protocols
- **Holistic Approach**: Comprehensive health assessment

## 🚀 Future Features

- [ ] Video analysis and comparison
- [ ] Mobile application support
- [ ] Multi-patient tracking system
- [ ] Telemedicine integration
- [ ] Wearable device support
- [ ] AI-powered exercise recommendations

## 🤝 Contributing

We welcome your contributions! Please:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google MediaPipe**: Excellent pose estimation model
- **Gradio Team**: User-friendly web interface
- **OpenCV Community**: Image processing tools
- **Physiotherapy Community**: Clinical guidance and feedback

## 📞 Contact

- **Developer**: Sems Kurtoglu
- **GitHub**: [@semskurto](https://github.com/semskurto)
- **Project**: [pose-think](https://github.com/semskurto/pose-think)

---

**⚠️ Important Note**: This system does not provide medical diagnosis. For serious health issues, always consult a healthcare professional.
