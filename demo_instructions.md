# 🎯 Demo Kullanım Talimatları

## 🚀 Hızlı Başlangıç

### 1. Uygulamayı Başlatın
```bash
python physiotherapy_assessment_app.py
```

### 2. <PERSON><PERSON>ı<PERSON>ıda Açın
- Uygulama başladığında verilen URL'yi tarayı<PERSON>ınızda açın
- Genellikle: `http://localhost:7861`

### 3. Kamera İzni Verin
- Tarayıcı kamera izni istediğinde "İzin Ver" seçin

## 🎭 Demo Senaryoları

### Senaryo 1: Normal Postür Testi
1. **Pozisyon**: Kameraya tam vücut görünecek şekilde durun
2. **<PERSON><PERSON>ş**: <PERSON><PERSON><PERSON>, rahat duruş pozisyonu
3. **Beklenen Sonuç**: 80+ postür skoru, minimal risk

### Senaryo 2: İleri Baş Pozisyonu Simülasyonu
1. **Pozisyon**: Başınızı öne doğru uzatın (bilgisayar kullanımı gibi)
2. **Duruş**: Omuzları öne alın
3. **Beklenen Sonuç**: Forward head posture tespiti, orta-yüksek risk

### Senaryo 3: Omuz Asimetrisi Testi
1. **Pozisyon**: Bir omzunuzu diğerinden yüksek tutun
2. **Duruş**: Asimetrik duruş
3. **Beklenen Sonuç**: Omuz asimetrisi tespiti, kompensasyon paternleri

### Senaryo 4: Pelvik Tilt Simülasyonu
1. **Pozisyon**: Kalçanızı öne veya arkaya itin
2. **Duruş**: Abartılı lordoz veya kifoz
3. **Beklenen Sonuç**: Pelvik tilt tespiti, spinal eğrilik uyarıları

## 🎛️ Kontrol Paneli Kullanımı

### Hassasiyet Ayarları
- **0.3-0.4**: Çok hassas (gürültülü ortam için)
- **0.5**: Varsayılan (çoğu durum için ideal)
- **0.6-0.9**: Az hassas (net görüntü gerekli)

### Hasta Profili
- **Yaş**: 18-80 arası (egzersiz planını etkiler)
- **Deneyim**: Başlangıç/Orta/İleri (zorluk seviyesi)

## 📊 Sonuçları Anlama

### Postür Skoru
- **90-100**: 🟢 Mükemmel - Koruyucu egzersizler
- **80-89**: 🟡 İyi - Hafif düzeltmeler
- **70-79**: 🟠 Orta - Düzenli egzersiz gerekli
- **60-69**: 🔴 Zayıf - Profesyonel değerlendirme
- **<60**: 🚨 Kritik - Acil müdahale

### Risk Seviyeleri
- **Düşük**: Koruyucu önlemler yeterli
- **Orta**: Düzenli egzersiz ve takip
- **Yüksek**: Profesyonel rehberlik gerekli
- **Kritik**: Acil tıbbi değerlendirme

## 🏋️ Egzersiz Planları

### Basit Egzersiz Planı
- Genel öneriler
- Günlük rutinler
- Basit egzersizler

### Klinik Tedavi Planı
- Detaylı değerlendirme
- Kişiselleştirilmiş egzersizler
- Takip programı
- Profesyonel öneriler

## 🔧 Sorun Giderme

### Kamera Çalışmıyor
1. Tarayıcı izinlerini kontrol edin
2. Başka uygulamaların kamerayı kullanmadığından emin olun
3. Tarayıcıyı yeniden başlatın

### Vücut Tespit Edilmiyor
1. Tam vücut görünür olmalı
2. Arka planı düz ve açık renk yapın
3. Işığı artırın
4. Hassasiyet ayarını düşürün

### Yavaş Çalışma
1. Diğer uygulamaları kapatın
2. Tarayıcı sekmelerini azaltın
3. Kamera çözünürlüğünü düşürün

## 📱 Tarayıcı Uyumluluğu

### Önerilen Tarayıcılar
- ✅ **Chrome**: En iyi performans
- ✅ **Firefox**: İyi uyumluluk
- ✅ **Safari**: macOS için uygun
- ⚠️ **Edge**: Temel özellikler

### Mobil Cihazlar
- Tablet: Desteklenir
- Telefon: Sınırlı destek (ekran boyutu)

## 🎥 Demo Video Önerileri

### Kayıt İpuçları
1. **Aydınlatma**: Yeterli ışık sağlayın
2. **Arka Plan**: Düz, tek renk tercih edin
3. **Mesafe**: 2-3 metre uzaklık ideal
4. **Açı**: Kamera göz hizasında olmalı

### Demo Akışı
1. Normal duruş → Mükemmel skor gösterimi
2. Kötü postür → Risk tespiti
3. Egzersiz planı oluşturma
4. Klinik tedavi planı alma

## 📈 Performans Metrikleri

### Beklenen Değerler
- **FPS**: 15-30 (cihaza bağlı)
- **Gecikme**: <100ms
- **Doğruluk**: %90+ (iyi koşullarda)
- **Tespit Oranı**: %95+ (uygun pozisyonda)

## 🎯 Demo Hedefleri

### Teknik Gösterim
- Gerçek zamanlı analiz
- Doğru landmark tespiti
- Hızlı işleme

### Klinik Değer
- Profesyonel değerlendirme
- Anlamlı öneriler
- Pratik egzersiz planları

### Kullanıcı Deneyimi
- Kolay kullanım
- Anlaşılır sonuçlar
- Etkileşimli arayüz

---

**💡 İpucu**: En iyi demo deneyimi için iyi aydınlatmalı, düz arka planlı bir ortamda test yapın!
